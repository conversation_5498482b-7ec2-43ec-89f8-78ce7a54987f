package cmd

import (
	"flag"
)

func parseWithFlag(host *string, isHttp *bool, filePath *string) {
	// 定义命令行参数
	host = flag.String("host", "localhost", "目标主机")
	isHttp = flag.Bool("http", false, "是否使用标准http日志格式")
	filePath = flag.String("file", "", "日志文件路径")

	// 解析命令行参数
	flag.Parse()

}

func main() {
	// 解析cmd命令
	var host string
	var isHttp bool
	var filePath string
	parseWithFlag(&host, &isHttp, &filePath)

}
